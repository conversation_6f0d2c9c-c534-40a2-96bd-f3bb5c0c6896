/* Reset y variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
    --text-light: #ffffff;
    --text-dark: #333333;
    --gradient-space: linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-light);
    background: var(--gradient-space);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-svg {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 2rem;
    padding: 0 2rem;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.stars-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
}

.star:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.star:nth-child(3) {
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.star:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: 0.5s;
}

.star:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 1.5s;
}

@keyframes twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

.hero-content {
    animation: slideInLeft 1s ease-out;
    position: relative;
    z-index: 1;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.5rem;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--primary-color), #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), #0099cc);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.hero-3d {
    height: 500px;
    position: relative;
    animation: slideInRight 1s ease-out;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3), transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2), transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1), transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 50px rgba(0, 212, 255, 0.1);
}

/* Beautiful CSS Solar System */
.solar-system {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    perspective: 1000px;
}

/* Sun */
.sun {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background:
        radial-gradient(circle at 30% 30%, #ffeb3b, #ff9800, #ff5722);
    box-shadow:
        0 0 30px #ffeb3b,
        0 0 60px #ff9800,
        0 0 90px #ff5722,
        inset -10px -10px 20px rgba(255, 87, 34, 0.3);
    animation: sunPulse 4s ease-in-out infinite alternate;
    z-index: 10;
}

.sun-core {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
    animation: coreRotate 8s linear infinite;
}

.sun-flare {
    position: absolute;
    top: -20px;
    left: -20px;
    width: calc(100% + 40px);
    height: calc(100% + 40px);
    border-radius: 50%;
    background: radial-gradient(circle, transparent 60%, rgba(255, 235, 59, 0.2) 100%);
    animation: flareGlow 3s ease-in-out infinite alternate;
}

/* Planet Orbits */
.planet-orbit {
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%) rotateX(70deg);
}

.orbit-1 {
    width: 160px;
    height: 160px;
    animation: orbit 12s linear infinite;
}

.orbit-2 {
    width: 240px;
    height: 240px;
    animation: orbit 18s linear infinite;
}

.orbit-3 {
    width: 320px;
    height: 320px;
    animation: orbit 25s linear infinite;
}

/* Planets */
.planet {
    position: absolute;
    border-radius: 50%;
    transform: rotateX(-70deg);
    overflow: hidden;
    animation: planetSpin 8s linear infinite;
}

/* Mercury - Planeta rocoso gris */
.planet-1 {
    top: -18px;
    left: -18px;
    width: 36px;
    height: 36px;
    background:
        radial-gradient(circle at 25% 25%, #d4d4d4, #a0a0a0, #707070, #404040),
        radial-gradient(circle at 70% 30%, rgba(200, 200, 200, 0.3), transparent 50%);
    box-shadow:
        inset -8px -8px 16px rgba(0, 0, 0, 0.6),
        inset 4px 4px 8px rgba(255, 255, 255, 0.1),
        0 0 20px rgba(160, 160, 160, 0.3);
}

/* Venus - Planeta dorado con atmósfera densa */
.planet-2 {
    top: -24px;
    left: -24px;
    width: 48px;
    height: 48px;
    background:
        radial-gradient(circle at 30% 20%, #ffd700, #ffb347, #ff8c00, #cc6600),
        radial-gradient(circle at 60% 70%, rgba(255, 215, 0, 0.4), transparent 60%);
    box-shadow:
        inset -12px -12px 20px rgba(0, 0, 0, 0.4),
        inset 6px 6px 12px rgba(255, 255, 255, 0.2),
        0 0 30px rgba(255, 215, 0, 0.5);
}

/* Earth - Planeta azul y verde */
.planet-3 {
    top: -30px;
    left: -30px;
    width: 60px;
    height: 60px;
    background:
        radial-gradient(circle at 30% 20%, #4fc3f7, #2196f3, #1976d2, #0d47a1),
        radial-gradient(circle at 60% 60%, rgba(76, 175, 80, 0.8), transparent 40%),
        radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.6), transparent 30%);
    box-shadow:
        inset -15px -15px 25px rgba(0, 0, 0, 0.4),
        inset 8px 8px 15px rgba(255, 255, 255, 0.2),
        0 0 40px rgba(33, 150, 243, 0.4);
}

/* Planet Surfaces */
.planet-surface {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    opacity: 0.8;
}

/* Mercury surface - Cráteres */
.surface-1 {
    background:
        radial-gradient(circle at 15% 30%, rgba(0, 0, 0, 0.4) 0%, transparent 15%),
        radial-gradient(circle at 60% 20%, rgba(0, 0, 0, 0.3) 0%, transparent 12%),
        radial-gradient(circle at 80% 70%, rgba(0, 0, 0, 0.3) 0%, transparent 10%),
        radial-gradient(circle at 30% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 20%);
    animation: surfaceMove1 15s linear infinite;
}

/* Venus surface - Nubes densas */
.surface-2 {
    background:
        radial-gradient(ellipse at 20% 40%, rgba(255, 255, 255, 0.3) 0%, transparent 60%),
        radial-gradient(ellipse at 70% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
        radial-gradient(ellipse at 50% 80%, rgba(255, 255, 255, 0.25) 0%, transparent 55%);
    animation: surfaceMove2 20s linear infinite;
}

/* Earth surface - Continentes y océanos */
.surface-3 {
    background:
        radial-gradient(ellipse at 20% 30%, rgba(76, 175, 80, 0.9) 0%, transparent 40%),
        radial-gradient(ellipse at 70% 60%, rgba(139, 69, 19, 0.7) 0%, transparent 35%),
        radial-gradient(ellipse at 40% 80%, rgba(76, 175, 80, 0.8) 0%, transparent 30%),
        radial-gradient(circle at 60% 20%, rgba(255, 255, 255, 0.4) 0%, transparent 25%);
    animation: surfaceMove3 25s linear infinite;
}

/* Planet Atmospheres */
.planet-atmosphere {
    position: absolute;
    top: -3px;
    left: -3px;
    width: calc(100% + 6px);
    height: calc(100% + 6px);
    border-radius: 50%;
}

/* Mercury - Atmósfera muy tenue */
.atm-1 {
    background: radial-gradient(circle, transparent 85%, rgba(200, 200, 200, 0.2) 100%);
    animation: atmosphereGlow1 4s ease-in-out infinite alternate;
}

/* Venus - Atmósfera densa dorada */
.atm-2 {
    background: radial-gradient(circle, transparent 75%, rgba(255, 215, 0, 0.4) 100%);
    animation: atmosphereGlow2 3s ease-in-out infinite alternate;
}

/* Earth - Atmósfera azul */
.atm-3 {
    background: radial-gradient(circle, transparent 80%, rgba(135, 206, 250, 0.5) 100%);
    animation: atmosphereGlow3 5s ease-in-out infinite alternate;
}

/* Planet Ring */
.planet-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 90px;
    height: 90px;
    transform: translate(-50%, -50%) rotateX(75deg);
    border: 3px solid transparent;
    border-radius: 50%;
    background:
        linear-gradient(transparent, transparent),
        conic-gradient(from 0deg,
            rgba(255, 215, 0, 0.6) 0deg,
            rgba(255, 255, 255, 0.3) 60deg,
            rgba(255, 215, 0, 0.4) 120deg,
            rgba(255, 255, 255, 0.2) 180deg,
            rgba(255, 215, 0, 0.6) 240deg,
            rgba(255, 255, 255, 0.3) 300deg,
            rgba(255, 215, 0, 0.6) 360deg);
    background-clip: padding-box, border-box;
    animation: ringRotate 30s linear infinite;
    z-index: -1;
}

.planet-ring::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background:
        conic-gradient(from 0deg,
            rgba(255, 215, 0, 0.3) 0deg,
            transparent 60deg,
            rgba(255, 215, 0, 0.2) 120deg,
            transparent 180deg,
            rgba(255, 215, 0, 0.3) 240deg,
            transparent 300deg,
            rgba(255, 215, 0, 0.3) 360deg);
    z-index: -1;
}

@keyframes ringRotate {
    from { transform: translate(-50%, -50%) rotateX(75deg) rotateZ(0deg); }
    to { transform: translate(-50%, -50%) rotateX(75deg) rotateZ(360deg); }
}

/* Stars Field */
.stars-field {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.star-particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 0 6px #ffffff;
    animation: starTwinkle 3s ease-in-out infinite;
}

.star-particle:nth-child(1) { top: 20%; left: 15%; animation-delay: 0s; }
.star-particle:nth-child(2) { top: 30%; left: 80%; animation-delay: 1s; }
.star-particle:nth-child(3) { top: 60%; left: 20%; animation-delay: 2s; }
.star-particle:nth-child(4) { top: 80%; left: 70%; animation-delay: 0.5s; }
.star-particle:nth-child(5) { top: 10%; left: 60%; animation-delay: 1.5s; }
.star-particle:nth-child(6) { top: 70%; left: 90%; animation-delay: 2.5s; }
.star-particle:nth-child(7) { top: 40%; left: 10%; animation-delay: 3s; }
.star-particle:nth-child(8) { top: 90%; left: 40%; animation-delay: 0.8s; }

/* Animations */
@keyframes sunPulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    100% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes coreRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes flareGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

@keyframes orbit {
    from { transform: translate(-50%, -50%) rotateX(70deg) rotateZ(0deg); }
    to { transform: translate(-50%, -50%) rotateX(70deg) rotateZ(360deg); }
}

@keyframes planetSpin {
    from { transform: rotateX(-70deg) rotateY(0deg); }
    to { transform: rotateX(-70deg) rotateY(360deg); }
}

@keyframes surfaceMove1 {
    0% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-5px); }
    50% { transform: translateX(-15px) translateY(0); }
    75% { transform: translateX(-10px) translateY(5px); }
    100% { transform: translateX(0) translateY(0); }
}

@keyframes surfaceMove2 {
    0% { transform: translateX(0) rotate(0deg); }
    100% { transform: translateX(-30px) rotate(360deg); }
}

@keyframes surfaceMove3 {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    33% { transform: translateX(-15px) translateY(-8px) rotate(120deg); }
    66% { transform: translateX(-25px) translateY(8px) rotate(240deg); }
    100% { transform: translateX(0) translateY(0) rotate(360deg); }
}

@keyframes atmosphereGlow1 {
    0% { opacity: 0.2; transform: scale(1); }
    50% { opacity: 0.4; transform: scale(1.05); }
    100% { opacity: 0.2; transform: scale(1); }
}

@keyframes atmosphereGlow2 {
    0% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.08); }
    100% { opacity: 0.4; transform: scale(1); }
}

@keyframes atmosphereGlow3 {
    0% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.06); }
    100% { opacity: 0.5; transform: scale(1); }
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.5); }
}

.planet-info-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.8));
    backdrop-filter: blur(15px);
    padding: 20px 25px;
    border-radius: 20px;
    border: 2px solid rgba(0, 212, 255, 0.4);
    z-index: 20;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 212, 255, 0.2);
    animation: overlayGlow 4s ease-in-out infinite alternate;
}

.planet-info-overlay h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.planet-info-overlay p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    margin: 0;
}

@keyframes overlayGlow {
    0% {
        border-color: rgba(0, 212, 255, 0.4);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(0, 212, 255, 0.2);
    }
    100% {
        border-color: rgba(0, 212, 255, 0.6);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(0, 212, 255, 0.4);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sections */
.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-color);
}

/* Planets Section */
.planets-section {
    padding: 5rem 0;
}

.planets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.planet-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
}

.planet-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.planet-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.planet-card:hover .planet-image {
    transform: scale(1.1);
}

.planet-info {
    padding: 1.5rem;
}

.planet-info h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Missions Section */
.missions-section {
    padding: 5rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.missions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.mission-video video {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.mission-text h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.mission-text ul {
    margin-top: 1rem;
    padding-left: 1.5rem;
}

.mission-text li {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
}

.audio-section {
    text-align: center;
    margin-bottom: 3rem;
}

.audio-section h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.audio-section audio {
    margin: 1rem 0;
    filter: sepia(100%) saturate(200%) hue-rotate(180deg);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Footer */
.footer {
    background: var(--secondary-color);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 2rem 1rem;
        gap: 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-3d {
        height: 400px;
        order: -1;
    }

    .solar-system {
        width: 300px;
        height: 300px;
    }

    .sun {
        width: 60px;
        height: 60px;
    }

    .orbit-1 { width: 120px; height: 120px; }
    .orbit-2 { width: 180px; height: 180px; }
    .orbit-3 { width: 240px; height: 240px; }

    .planet-1 { width: 20px; height: 20px; top: -10px; left: -10px; }
    .planet-2 { width: 30px; height: 30px; top: -15px; left: -15px; }
    .planet-3 { width: 35px; height: 35px; top: -17px; left: -17px; }

    .planet-ring { width: 60px; height: 60px; }

    .planet-info-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 15px 20px;
    }

    .missions-content {
        grid-template-columns: 1fr;
    }

    .planets-grid {
        grid-template-columns: 1fr;
    }

    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-3d {
        height: 350px;
    }

    .solar-system {
        width: 250px;
        height: 250px;
    }

    .sun {
        width: 50px;
        height: 50px;
    }

    .orbit-1 { width: 100px; height: 100px; }
    .orbit-2 { width: 150px; height: 150px; }
    .orbit-3 { width: 200px; height: 200px; }

    .planet-1 { width: 15px; height: 15px; top: -7px; left: -7px; }
    .planet-2 { width: 25px; height: 25px; top: -12px; left: -12px; }
    .planet-3 { width: 30px; height: 30px; top: -15px; left: -15px; }

    .planet-ring { width: 50px; height: 50px; }

    .planet-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 15px;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }
}
