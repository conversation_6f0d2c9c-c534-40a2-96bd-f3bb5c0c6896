/* Reset y variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
    --text-light: #ffffff;
    --text-dark: #333333;
    --gradient-space: linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-light);
    background: var(--gradient-space);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-svg {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 2rem;
    padding: 0 2rem;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.stars-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
}

.star:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.star:nth-child(3) {
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.star:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: 0.5s;
}

.star:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 1.5s;
}

@keyframes twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

.hero-content {
    animation: slideInLeft 1s ease-out;
    position: relative;
    z-index: 1;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.5rem;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--primary-color), #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), #0099cc);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.hero-3d {
    height: 500px;
    position: relative;
    animation: slideInRight 1s ease-out;
    background: radial-gradient(circle at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.hero-3d spline-viewer {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    background: transparent;
}

/* CSS 3D Planet Fallback */
.css-planet-fallback {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    display: none; /* Se mostrará si Spline falla */
}

.planet {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 50px auto;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, #4a90e2, #2c5aa0, #1a365d);
    box-shadow:
        inset -20px -20px 50px rgba(0, 0, 0, 0.5),
        0 0 50px rgba(74, 144, 226, 0.3);
    animation: planetRotate 20s linear infinite;
    overflow: hidden;
}

.planet-surface {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 60%, rgba(0, 100, 200, 0.4) 0%, transparent 40%),
        radial-gradient(circle at 40% 80%, rgba(0, 150, 255, 0.3) 0%, transparent 30%);
    animation: surfaceMove 30s linear infinite;
}

.planet-atmosphere {
    position: absolute;
    top: -10px;
    left: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    border-radius: 50%;
    background: radial-gradient(circle, transparent 70%, rgba(0, 212, 255, 0.2) 100%);
    animation: atmosphereGlow 4s ease-in-out infinite alternate;
}

.orbit-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 350px;
    height: 350px;
    transform: translate(-50%, -50%) rotateX(75deg);
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 50%;
    animation: orbitRotate 15s linear infinite;
}

@keyframes planetRotate {
    from { transform: rotateY(0deg); }
    to { transform: rotateY(360deg); }
}

@keyframes surfaceMove {
    from { transform: translateX(0); }
    to { transform: translateX(-50px); }
}

@keyframes atmosphereGlow {
    from { opacity: 0.6; }
    to { opacity: 1; }
}

@keyframes orbitRotate {
    from { transform: translate(-50%, -50%) rotateX(75deg) rotateZ(0deg); }
    to { transform: translate(-50%, -50%) rotateX(75deg) rotateZ(360deg); }
}

/* Mostrar fallback si Spline no carga */
.hero-3d spline-viewer:empty + .css-planet-fallback,
.hero-3d spline-viewer[data-error] + .css-planet-fallback {
    display: block;
}

.planet-info-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.3);
    z-index: 3;
}

.planet-info-overlay h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.planet-info-overlay p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sections */
.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-color);
}

/* Planets Section */
.planets-section {
    padding: 5rem 0;
}

.planets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.planet-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
}

.planet-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.planet-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.planet-card:hover .planet-image {
    transform: scale(1.1);
}

.planet-info {
    padding: 1.5rem;
}

.planet-info h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Missions Section */
.missions-section {
    padding: 5rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.missions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.mission-video video {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.mission-text h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.mission-text ul {
    margin-top: 1rem;
    padding-left: 1.5rem;
}

.mission-text li {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
}

.audio-section {
    text-align: center;
    margin-bottom: 3rem;
}

.audio-section h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.audio-section audio {
    margin: 1rem 0;
    filter: sepia(100%) saturate(200%) hue-rotate(180deg);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Footer */
.footer {
    background: var(--secondary-color);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 2rem 1rem;
        gap: 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-3d {
        height: 400px;
        order: -1;
    }

    .css-planet-fallback {
        width: 250px;
        height: 250px;
    }

    .planet {
        width: 150px;
        height: 150px;
        margin: 50px auto;
    }

    .orbit-ring {
        width: 250px;
        height: 250px;
    }

    .planet-info-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 15px 20px;
    }

    .missions-content {
        grid-template-columns: 1fr;
    }

    .planets-grid {
        grid-template-columns: 1fr;
    }

    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-3d {
        height: 350px;
    }

    .css-planet-fallback {
        width: 200px;
        height: 200px;
    }

    .planet {
        width: 120px;
        height: 120px;
        margin: 40px auto;
    }

    .orbit-ring {
        width: 200px;
        height: 200px;
    }

    .planet-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 15px;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }
}
