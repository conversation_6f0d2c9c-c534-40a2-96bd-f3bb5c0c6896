/* Reset y variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
    --text-light: #ffffff;
    --text-dark: #333333;
    --gradient-space: linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-light);
    background: var(--gradient-space);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-svg {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 2rem;
    padding: 0 2rem;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.stars-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
}

.star:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.star:nth-child(3) {
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.star:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: 0.5s;
}

.star:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 1.5s;
}

@keyframes twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

.hero-content {
    animation: slideInLeft 1s ease-out;
    position: relative;
    z-index: 1;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.5rem;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--primary-color), #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), #0099cc);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.hero-3d {
    height: 500px;
    position: relative;
    animation: slideInRight 1s ease-out;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3), transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2), transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1), transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 50px rgba(0, 212, 255, 0.1);
}

/* True 3D Spherical Earth */
.earth-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    perspective: 1200px;
    perspective-origin: center center;
}

.earth {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 50px auto;
    border-radius: 50%;
    transform-style: preserve-3d;
    animation: earthRotate3D 30s linear infinite;

    /* Base sphere with proper 3D lighting */
    background:
        radial-gradient(circle at 25% 25%, #87ceeb 0%, #4fc3f7 25%, #2196f3 50%, #1976d2 75%, #0d47a1 100%);

    /* 3D sphere effect with multiple shadows */
    box-shadow:
        /* Inner shadow for sphere depth */
        inset -40px -40px 80px rgba(0, 0, 0, 0.4),
        inset -20px -20px 40px rgba(0, 0, 0, 0.2),
        /* Highlight for 3D effect */
        inset 20px 20px 40px rgba(255, 255, 255, 0.1),
        inset 10px 10px 20px rgba(255, 255, 255, 0.05),
        /* Outer glow */
        0 0 60px rgba(33, 150, 243, 0.4),
        0 0 120px rgba(33, 150, 243, 0.2);

    overflow: hidden;
}

/* Multiple surface layers for 3D depth */
.earth-surface {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;

    /* Continents with proper 3D perspective */
    background:
        /* Africa */
        radial-gradient(ellipse 40px 60px at 45% 45%, rgba(76, 175, 80, 0.9) 0%, transparent 70%),
        /* South America */
        radial-gradient(ellipse 25px 50px at 35% 60%, rgba(76, 175, 80, 0.8) 0%, transparent 70%),
        /* North America */
        radial-gradient(ellipse 35px 45px at 25% 35%, rgba(76, 175, 80, 0.85) 0%, transparent 70%),
        /* Asia */
        radial-gradient(ellipse 50px 40px at 65% 30%, rgba(76, 175, 80, 0.9) 0%, transparent 70%),
        /* Australia */
        radial-gradient(ellipse 20px 15px at 75% 65%, rgba(76, 175, 80, 0.8) 0%, transparent 70%),
        /* Desert areas */
        radial-gradient(ellipse 30px 20px at 50% 35%, rgba(139, 69, 19, 0.6) 0%, transparent 70%);

    animation: surfaceRotate3D 35s linear infinite;

    /* 3D surface curvature */
    box-shadow: inset -10px -10px 20px rgba(0, 0, 0, 0.2);
}

.earth-clouds {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;

    /* Cloud formations with 3D depth */
    background:
        radial-gradient(ellipse 60px 30px at 30% 40%, rgba(255, 255, 255, 0.4) 0%, transparent 60%),
        radial-gradient(ellipse 40px 20px at 70% 25%, rgba(255, 255, 255, 0.35) 0%, transparent 60%),
        radial-gradient(ellipse 50px 25px at 50% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 60%),
        radial-gradient(ellipse 35px 18px at 20% 60%, rgba(255, 255, 255, 0.25) 0%, transparent 60%),
        radial-gradient(ellipse 45px 22px at 80% 50%, rgba(255, 255, 255, 0.3) 0%, transparent 60%);

    animation: cloudsMove3D 40s linear infinite;

    /* Cloud layer depth */
    box-shadow: inset -5px -5px 10px rgba(0, 0, 0, 0.1);
}

/* Day/Night Terminator Line */
.earth-terminator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;

    /* Day/night shadow effect */
    background:
        linear-gradient(135deg,
            transparent 0%,
            transparent 45%,
            rgba(0, 0, 0, 0.3) 50%,
            rgba(0, 0, 0, 0.6) 55%,
            rgba(0, 0, 0, 0.8) 60%,
            rgba(0, 0, 0, 0.9) 100%);

    animation: terminatorMove 30s linear infinite;

    /* Terminator depth */
    box-shadow: inset -30px -30px 60px rgba(0, 0, 0, 0.4);
}

.earth-atmosphere {
    position: absolute;
    top: -8px;
    left: -8px;
    width: calc(100% + 16px);
    height: calc(100% + 16px);
    border-radius: 50%;

    /* Atmospheric glow with 3D effect */
    background:
        radial-gradient(circle, transparent 80%, rgba(135, 206, 250, 0.6) 90%, rgba(135, 206, 250, 0.3) 100%);

    animation: atmospherePulse3D 8s ease-in-out infinite alternate;
}

.earth-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    width: calc(100% + 40px);
    height: calc(100% + 40px);
    border-radius: 50%;

    /* Outer space glow */
    background:
        radial-gradient(circle, transparent 75%, rgba(33, 150, 243, 0.3) 85%, rgba(33, 150, 243, 0.1) 100%);

    animation: glowPulse3D 12s ease-in-out infinite alternate;
}

/* Stars Background for 3D Earth */
.stars-background-3d {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.star-3d {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 0 6px #ffffff;
    animation: starTwinkle3d 4s ease-in-out infinite;
}

.star-3d:nth-child(1) { top: 15%; left: 20%; animation-delay: 0s; }
.star-3d:nth-child(2) { top: 25%; left: 80%; animation-delay: 1s; }
.star-3d:nth-child(3) { top: 45%; left: 10%; animation-delay: 2s; }
.star-3d:nth-child(4) { top: 65%; left: 85%; animation-delay: 0.5s; }
.star-3d:nth-child(5) { top: 80%; left: 30%; animation-delay: 1.5s; }
.star-3d:nth-child(6) { top: 10%; left: 70%; animation-delay: 2.5s; }
.star-3d:nth-child(7) { top: 35%; left: 90%; animation-delay: 3s; }
.star-3d:nth-child(8) { top: 75%; left: 15%; animation-delay: 0.8s; }
.star-3d:nth-child(9) { top: 55%; left: 60%; animation-delay: 1.8s; }
.star-3d:nth-child(10) { top: 90%; left: 75%; animation-delay: 2.8s; }

/* True 3D Earth Animations */
@keyframes earthRotate3D {
    0% {
        transform: rotateY(0deg) rotateX(0deg);
    }
    25% {
        transform: rotateY(90deg) rotateX(5deg);
    }
    50% {
        transform: rotateY(180deg) rotateX(0deg);
    }
    75% {
        transform: rotateY(270deg) rotateX(-5deg);
    }
    100% {
        transform: rotateY(360deg) rotateX(0deg);
    }
}

@keyframes surfaceRotate3D {
    0% {
        transform: translateX(0) rotateZ(0deg);
        opacity: 1;
    }
    25% {
        transform: translateX(-50px) rotateZ(90deg);
        opacity: 0.9;
    }
    50% {
        transform: translateX(-100px) rotateZ(180deg);
        opacity: 0.8;
    }
    75% {
        transform: translateX(-150px) rotateZ(270deg);
        opacity: 0.9;
    }
    100% {
        transform: translateX(-200px) rotateZ(360deg);
        opacity: 1;
    }
}

@keyframes cloudsMove3D {
    0% {
        transform: translateX(0) translateY(0) rotateZ(0deg);
        opacity: 0.8;
    }
    33% {
        transform: translateX(-70px) translateY(-10px) rotateZ(120deg);
        opacity: 0.6;
    }
    66% {
        transform: translateX(-140px) translateY(10px) rotateZ(240deg);
        opacity: 0.7;
    }
    100% {
        transform: translateX(-210px) translateY(0) rotateZ(360deg);
        opacity: 0.8;
    }
}

@keyframes atmospherePulse3D {
    0% {
        opacity: 0.6;
        transform: scale(1) rotateZ(0deg);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.03) rotateZ(180deg);
    }
    100% {
        opacity: 0.9;
        transform: scale(1.05) rotateZ(360deg);
    }
}

@keyframes glowPulse3D {
    0% {
        opacity: 0.3;
        transform: scale(1) rotateZ(0deg);
    }
    33% {
        opacity: 0.5;
        transform: scale(1.02) rotateZ(120deg);
    }
    66% {
        opacity: 0.4;
        transform: scale(1.04) rotateZ(240deg);
    }
    100% {
        opacity: 0.6;
        transform: scale(1.06) rotateZ(360deg);
    }
}

@keyframes starTwinkle3d {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) rotateZ(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.5) rotateZ(180deg);
    }
}

@keyframes terminatorMove {
    0% {
        transform: rotateZ(0deg);
    }
    100% {
        transform: rotateZ(360deg);
    }
}

/* Removed old planet styles - now using beautiful 3D Earth */

/* Cleaned up - using new 3D Earth animations above */

.planet-info-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.8));
    backdrop-filter: blur(15px);
    padding: 20px 25px;
    border-radius: 20px;
    border: 2px solid rgba(0, 212, 255, 0.4);
    z-index: 20;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 212, 255, 0.2);
    animation: overlayGlow 4s ease-in-out infinite alternate;
}

.planet-info-overlay h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.planet-info-overlay p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    margin: 0;
}

@keyframes overlayGlow {
    0% {
        border-color: rgba(0, 212, 255, 0.4);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(0, 212, 255, 0.2);
    }
    100% {
        border-color: rgba(0, 212, 255, 0.6);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(0, 212, 255, 0.4);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sections */
.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-color);
}

/* Planets Section */
.planets-section {
    padding: 5rem 0;
}

.planets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.planet-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
}

.planet-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.planet-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.planet-card:hover .planet-image {
    transform: scale(1.1);
}

.planet-info {
    padding: 1.5rem;
}

.planet-info h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Missions Section */
.missions-section {
    padding: 5rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.missions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.mission-video video {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.mission-text h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.mission-text ul {
    margin-top: 1rem;
    padding-left: 1.5rem;
}

.mission-text li {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
}

.audio-section {
    text-align: center;
    margin-bottom: 3rem;
}

.audio-section h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.audio-section audio {
    margin: 1rem 0;
    filter: sepia(100%) saturate(200%) hue-rotate(180deg);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Footer */
.footer {
    background: var(--secondary-color);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 2rem 1rem;
        gap: 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-3d {
        height: 400px;
        order: -1;
    }

    .earth-container {
        width: 250px;
        height: 250px;
    }

    .earth {
        width: 150px;
        height: 150px;
        margin: 50px auto;
    }

    .planet-info-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 15px 20px;
    }

    .missions-content {
        grid-template-columns: 1fr;
    }

    .planets-grid {
        grid-template-columns: 1fr;
    }

    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-3d {
        height: 350px;
    }

    .earth-container {
        width: 200px;
        height: 200px;
    }

    .earth {
        width: 120px;
        height: 120px;
        margin: 40px auto;
    }

    .planet-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 15px;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }
}
