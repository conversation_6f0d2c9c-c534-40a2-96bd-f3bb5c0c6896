/* Reset y variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
    --text-light: #ffffff;
    --text-dark: #333333;
    --gradient-space: linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-light);
    background: var(--gradient-space);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-svg {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 2rem;
    padding: 0 2rem;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.stars-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
}

.star:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.star:nth-child(3) {
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.star:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: 0.5s;
}

.star:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 1.5s;
}

@keyframes twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

.hero-content {
    animation: slideInLeft 1s ease-out;
    position: relative;
    z-index: 1;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.5rem;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--primary-color), #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), #0099cc);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.hero-3d {
    height: 500px;
    position: relative;
    animation: slideInRight 1s ease-out;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3), transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2), transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1), transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 50px rgba(0, 212, 255, 0.1);
}

/* Elegant Spline 3D Container */
.spline-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    max-width: 600px;
    max-height: 600px;
    perspective: 1200px;
    border-radius: 30px;
    overflow: hidden;

    /* Electric Blue background with gradients */
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 100, 255, 0.8) 0%, rgba(0, 150, 255, 0.6) 30%, transparent 60%),
        radial-gradient(circle at 70% 70%, rgba(0, 200, 255, 0.7) 0%, rgba(0, 255, 255, 0.5) 25%, transparent 50%),
        linear-gradient(135deg, rgba(0, 100, 255, 0.3) 0%, rgba(0, 150, 255, 0.4) 50%, rgba(0, 200, 255, 0.2) 100%),
        #0066ff;

    /* Electric blue border and shadow effects */
    border: 2px solid rgba(0, 150, 255, 0.4);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 0 80px rgba(0, 100, 255, 0.6),
        0 0 120px rgba(0, 200, 255, 0.4),
        0 0 160px rgba(0, 255, 255, 0.2),
        inset 0 0 80px rgba(0, 150, 255, 0.1);

    /* Enhanced floating animation */
    animation: containerFloat 8s ease-in-out infinite alternate;
}

.spline-container spline-viewer {
    width: 100%;
    height: 100%;
    border-radius: 30px;
    background: transparent;

    /* Ensure Spline content is properly displayed */
    display: block;
    position: relative;
    z-index: 2;

    /* High quality rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;

    /* Smooth loading transition */
    transition: opacity 0.5s ease-in-out;
    opacity: 1;

    /* Prevent compression artifacts */
    backface-visibility: hidden;
    transform: translateZ(0);
    will-change: transform;
}

/* Floating particles background */
.spline-background-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 150, 255, 0.8);
    border-radius: 50%;
    box-shadow:
        0 0 10px rgba(0, 100, 255, 1.0),
        0 0 20px rgba(0, 200, 255, 0.6),
        0 0 30px rgba(0, 255, 255, 0.3);
    animation: floatParticle 8s ease-in-out infinite;
}

.floating-particle:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.floating-particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
    animation-duration: 10s;
}

.floating-particle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 4s;
    animation-duration: 12s;
}

.floating-particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 1s;
    animation-duration: 9s;
}

.floating-particle:nth-child(5) {
    top: 70%;
    left: 40%;
    animation-delay: 3s;
    animation-duration: 11s;
}

/* Interactive particles */
.fallback-3d-object:hover .floating-particle {
    animation-duration: 2s;
    transform: scale(1.5);
    opacity: 1;
}

.fallback-3d-object:active .floating-particle {
    animation-duration: 1s;
    transform: scale(2);
}

/* Interactive 3D Object - Hidden for Spline focus */
.fallback-3d-object {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    display: none !important; /* Hidden - focusing on Spline only */
    perspective: 1000px;
    z-index: 3;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    cursor: grab;
    user-select: none;
}

.fallback-3d-object:active {
    cursor: grabbing;
}

.fallback-3d-object:hover {
    transform: translate(-50%, -50%) scale(1.05);
}

/* Interactive state indicators */
.fallback-3d-object::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.fallback-3d-object:hover::before {
    opacity: 1;
    animation: pulseRing 2s ease-in-out infinite;
}

@keyframes pulseRing {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.6;
    }
}

/* Interactive Spline-like 3D Object */
.geometric-shape {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 25px auto;
    transform-style: preserve-3d;
    transform: scale(1) rotateX(15deg) rotateY(0deg);
    transition: transform 0.1s ease;
}

.shape-face {
    position: absolute;
    width: 150px;
    height: 150px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg,
        rgba(255, 0, 150, 0.4) 0%,
        rgba(138, 43, 226, 0.4) 30%,
        rgba(0, 191, 255, 0.4) 60%,
        rgba(0, 255, 255, 0.4) 100%);
    backdrop-filter: blur(15px);
    box-shadow:
        inset 0 0 30px rgba(255, 255, 255, 0.1),
        0 0 40px rgba(138, 43, 226, 0.3),
        0 0 60px rgba(0, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.shape-face:hover {
    border: 2px solid rgba(255, 255, 255, 0.6);
    box-shadow:
        inset 0 0 40px rgba(255, 255, 255, 0.2),
        0 0 60px rgba(138, 43, 226, 0.5),
        0 0 80px rgba(0, 255, 255, 0.4);
}

.face-front {
    transform: rotateY(0deg) translateZ(75px);
    background: linear-gradient(135deg,
        rgba(255, 0, 150, 0.5) 0%,
        rgba(138, 43, 226, 0.4) 100%);
}

.face-back {
    transform: rotateY(180deg) translateZ(75px);
    background: linear-gradient(135deg,
        rgba(0, 191, 255, 0.5) 0%,
        rgba(0, 255, 255, 0.4) 100%);
}

.face-right {
    transform: rotateY(90deg) translateZ(75px);
    background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.5) 0%,
        rgba(0, 191, 255, 0.4) 100%);
}

.face-left {
    transform: rotateY(-90deg) translateZ(75px);
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.5) 0%,
        rgba(255, 0, 150, 0.4) 100%);
}

.face-top {
    transform: rotateX(90deg) translateZ(75px);
    background: linear-gradient(135deg,
        rgba(255, 0, 150, 0.4) 0%,
        rgba(0, 255, 255, 0.5) 100%);
}

.face-bottom {
    transform: rotateX(-90deg) translateZ(75px);
    background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.4) 0%,
        rgba(0, 191, 255, 0.5) 100%);
}

/* Floating Rings */
.floating-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
}

.ring {
    position: absolute;
    border: 3px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg,
        rgba(138, 43, 226, 0.6),
        rgba(0, 191, 255, 0.6)) border-box;
    background-clip: border-box;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.ring-1 {
    top: 50%;
    left: 50%;
    width: 160px;
    height: 160px;
    transform: translate(-50%, -50%) rotateX(0deg);
    animation: ringRotate1 20s linear infinite;
}

.ring-2 {
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    transform: translate(-50%, -50%) rotateX(60deg);
    animation: ringRotate2 25s linear infinite reverse;
}

.ring-3 {
    top: 50%;
    left: 50%;
    width: 240px;
    height: 240px;
    transform: translate(-50%, -50%) rotateX(120deg);
    animation: ringRotate3 30s linear infinite;
}

/* Cleaned up old Earth styles */

/* Cleaned up old Earth animations */

/* Removed old planet styles - now using beautiful 3D Earth */

/* Cleaned up - using new 3D Earth animations above */

.spline-info-overlay {
    position: absolute;
    bottom: 25px;
    left: 25px;
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.95),
        rgba(26, 26, 46, 0.9),
        rgba(138, 43, 226, 0.1));
    backdrop-filter: blur(20px);
    padding: 25px 30px;
    border-radius: 25px;
    border: 2px solid transparent;
    background-clip: padding-box;
    z-index: 20;

    /* Enhanced glow effect */
    box-shadow:
        0 10px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(138, 43, 226, 0.4),
        0 0 60px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(255, 255, 255, 0.05);

    /* Animated border */
    position: relative;
    animation: splineOverlayGlow 5s ease-in-out infinite alternate;
}

.spline-info-overlay::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #ff0080, #8a2be2, #00bfff, #00ffff, #ff0080);
    border-radius: 25px;
    z-index: -1;
    animation: borderRotate 6s linear infinite;
}

.spline-info-overlay h3 {
    font-family: var(--font-primary);
    background: linear-gradient(45deg, #ff0080, #8a2be2, #00bfff, #00ffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
    font-size: 1.3rem;
    text-shadow: 0 0 15px rgba(138, 43, 226, 0.6);
    animation: textGlow 3s ease-in-out infinite alternate;
}

.spline-info-overlay p {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1rem;
    margin: 0;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

/* Electric Blue Container Animations */
@keyframes containerFloat {
    0% {
        transform: translate(-50%, -50%) translateY(0px) rotateZ(0deg) scale(1);
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.4),
            0 0 80px rgba(0, 100, 255, 0.6),
            0 0 120px rgba(0, 200, 255, 0.4),
            0 0 160px rgba(0, 255, 255, 0.2);
    }
    50% {
        transform: translate(-50%, -50%) translateY(-8px) rotateZ(0.5deg) scale(1.02);
        box-shadow:
            0 35px 70px rgba(0, 0, 0, 0.5),
            0 0 100px rgba(0, 150, 255, 0.8),
            0 0 150px rgba(0, 255, 255, 0.6),
            0 0 200px rgba(0, 200, 255, 0.3);
    }
    100% {
        transform: translate(-50%, -50%) translateY(-15px) rotateZ(1deg) scale(1.05);
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.6),
            0 0 120px rgba(0, 100, 255, 1.0),
            0 0 180px rgba(0, 255, 255, 0.8),
            0 0 240px rgba(0, 200, 255, 0.4);
    }
}

@keyframes borderRotate {
    0% {
        background: linear-gradient(45deg,
            #ff0080, #8a2be2, #00bfff, #00ffff, #ff0080);
    }
    25% {
        background: linear-gradient(135deg,
            #8a2be2, #00bfff, #00ffff, #ff0080, #8a2be2);
    }
    50% {
        background: linear-gradient(225deg,
            #00bfff, #00ffff, #ff0080, #8a2be2, #00bfff);
    }
    75% {
        background: linear-gradient(315deg,
            #00ffff, #ff0080, #8a2be2, #00bfff, #00ffff);
    }
    100% {
        background: linear-gradient(45deg,
            #ff0080, #8a2be2, #00bfff, #00ffff, #ff0080);
    }
}

@keyframes textGlow {
    0% {
        text-shadow: 0 0 15px rgba(138, 43, 226, 0.6);
    }
    100% {
        text-shadow:
            0 0 25px rgba(255, 0, 150, 0.8),
            0 0 35px rgba(0, 255, 255, 0.6);
    }
}

@keyframes floatParticle {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) translateX(-5px) scale(0.8);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-30px) translateX(15px) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes splineOverlayGlow {
    0% {
        box-shadow:
            0 10px 40px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(138, 43, 226, 0.4),
            0 0 60px rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow:
            0 15px 50px rgba(0, 0, 0, 0.5),
            0 0 40px rgba(255, 0, 150, 0.5),
            0 0 80px rgba(0, 255, 255, 0.3);
    }
    100% {
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.6),
            0 0 50px rgba(0, 191, 255, 0.6),
            0 0 100px rgba(138, 43, 226, 0.4);
    }
}

/* Spline-like 3D Object Animations */
@keyframes splineLikeRotate {
    0% {
        transform: rotateX(15deg) rotateY(0deg) rotateZ(0deg) scale(1);
    }
    25% {
        transform: rotateX(15deg) rotateY(90deg) rotateZ(5deg) scale(1.05);
    }
    50% {
        transform: rotateX(15deg) rotateY(180deg) rotateZ(0deg) scale(1);
    }
    75% {
        transform: rotateX(15deg) rotateY(270deg) rotateZ(-5deg) scale(1.05);
    }
    100% {
        transform: rotateX(15deg) rotateY(360deg) rotateZ(0deg) scale(1);
    }
}

@keyframes ringRotate1 {
    0% {
        transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateX(0deg) rotateY(360deg) rotateZ(0deg);
    }
}

@keyframes ringRotate2 {
    0% {
        transform: translate(-50%, -50%) rotateX(60deg) rotateY(0deg) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateX(60deg) rotateY(0deg) rotateZ(360deg);
    }
}

@keyframes ringRotate3 {
    0% {
        transform: translate(-50%, -50%) rotateX(120deg) rotateY(0deg) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateX(120deg) rotateY(360deg) rotateZ(180deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sections */
.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-color);
}

/* Planets Section */
.planets-section {
    padding: 5rem 0;
}

.planets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.planet-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
}

.planet-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.planet-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.planet-card:hover .planet-image {
    transform: scale(1.1);
}

.planet-info {
    padding: 1.5rem;
}

.planet-info h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Missions Section */
.missions-section {
    padding: 5rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.missions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

/* Video Placeholder Styles */
.video-placeholder {
    position: relative;
    width: 100%;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-placeholder:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 150, 255, 0.3);
}

.video-placeholder img {
    width: 100%;
    height: auto;
    display: block;
    transition: filter 0.3s ease;
}

.video-placeholder:hover img {
    filter: brightness(0.8);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    animation: playButtonPulse 2s ease-in-out infinite;
}

.video-placeholder:hover .play-button {
    transform: translate(-50%, -50%) scale(1.1);
}

.video-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px;
    color: white;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.video-placeholder:hover .video-info {
    transform: translateY(0);
}

.video-info h4 {
    margin: 0 0 5px 0;
    font-family: var(--font-primary);
    color: var(--primary-color);
}

.video-info p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

@keyframes playButtonPulse {
    0%, 100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
    }
}

.mission-text h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.mission-text ul {
    margin-top: 1rem;
    padding-left: 1.5rem;
}

.mission-text li {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
}

.audio-section {
    text-align: center;
    margin-bottom: 3rem;
}

.audio-section h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.audio-section audio {
    margin: 1rem 0;
    filter: sepia(100%) saturate(200%) hue-rotate(180deg);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Footer */
.footer {
    background: var(--secondary-color);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 2rem 1rem;
        gap: 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-3d {
        height: 400px;
        order: -1;
    }

    .spline-container {
        max-width: 400px;
        max-height: 400px;
        border-radius: 20px;
    }

    .spline-info-overlay {
        bottom: 15px;
        left: 15px;
        right: 15px;
        padding: 18px 22px;
        border-radius: 18px;
    }

    .spline-info-overlay h3 {
        font-size: 1.1rem;
    }

    .fallback-3d-object {
        width: 150px;
        height: 150px;
    }

    .geometric-shape {
        width: 80px;
        height: 80px;
        margin: 35px auto;
    }

    .shape-face {
        width: 80px;
        height: 80px;
    }

    .face-front { transform: rotateY(0deg) translateZ(40px); }
    .face-back { transform: rotateY(180deg) translateZ(40px); }
    .face-right { transform: rotateY(90deg) translateZ(40px); }
    .face-left { transform: rotateY(-90deg) translateZ(40px); }
    .face-top { transform: rotateX(90deg) translateZ(40px); }
    .face-bottom { transform: rotateX(-90deg) translateZ(40px); }

    .floating-rings {
        width: 150px;
        height: 150px;
    }

    .ring-1 { width: 120px; height: 120px; }
    .ring-2 { width: 150px; height: 150px; }
    .ring-3 { width: 180px; height: 180px; }

    .planet-info-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 15px 20px;
    }

    .missions-content {
        grid-template-columns: 1fr;
    }

    .planets-grid {
        grid-template-columns: 1fr;
    }

    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-3d {
        height: 350px;
    }

    .spline-container {
        max-width: 320px;
        max-height: 320px;
        border-radius: 15px;
    }

    .spline-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 20px;
        padding: 15px 18px;
        border-radius: 15px;
    }

    .spline-info-overlay h3 {
        font-size: 1rem;
    }

    .spline-info-overlay p {
        font-size: 0.9rem;
    }

    .planet-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 15px;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }
}
