/* Reset y variables CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #1a1a2e;
    --accent-color: #16213e;
    --text-light: #ffffff;
    --text-dark: #333333;
    --gradient-space: linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Roboto', sans-serif;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-light);
    background: var(--gradient-space);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-svg {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 2rem;
    padding: 0 2rem;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.stars-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
}

.star:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.star:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.star:nth-child(3) {
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.star:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: 0.5s;
}

.star:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 1.5s;
}

@keyframes twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

.hero-content {
    animation: slideInLeft 1s ease-out;
    position: relative;
    z-index: 1;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 0.5rem;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--primary-color), #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background: linear-gradient(45deg, var(--primary-color), #0099cc);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--font-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.hero-3d {
    height: 500px;
    position: relative;
    animation: slideInRight 1s ease-out;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3), transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2), transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1), transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #16213e 50%, #1a1a2e 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        inset 0 0 50px rgba(0, 212, 255, 0.1);
}

/* Elegant Spline 3D Container */
.spline-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    max-width: 500px;
    max-height: 500px;
    perspective: 1000px;
    border-radius: 25px;
    overflow: hidden;

    /* Elegant background with gradient */
    background:
        radial-gradient(circle at 30% 30%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(0, 191, 255, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* Elegant border and shadow */
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 0 50px rgba(0, 212, 255, 0.1),
        inset 0 0 50px rgba(255, 255, 255, 0.05);

    /* Subtle animation */
    animation: containerFloat 6s ease-in-out infinite alternate;
}

.spline-container spline-viewer {
    width: 100%;
    height: 100%;
    border-radius: 25px;
    background: transparent;

    /* Ensure Spline content is properly displayed */
    display: block;
    position: relative;
    z-index: 2;
}

/* Floating particles background */
.spline-background-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 212, 255, 0.6);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
    animation: floatParticle 8s ease-in-out infinite;
}

.floating-particle:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.floating-particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
    animation-duration: 10s;
}

.floating-particle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 4s;
    animation-duration: 12s;
}

.floating-particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 1s;
    animation-duration: 9s;
}

.floating-particle:nth-child(5) {
    top: 70%;
    left: 40%;
    animation-delay: 3s;
    animation-duration: 11s;
}

/* Fallback 3D Object */
.fallback-3d-object {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    display: none; /* Show if Spline fails */
    perspective: 1000px;
    z-index: 3;
}

/* Geometric Shape - 3D Cube */
.geometric-shape {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 40px auto;
    transform-style: preserve-3d;
    animation: cubeRotate 15s linear infinite;
}

.shape-face {
    position: absolute;
    width: 120px;
    height: 120px;
    border: 2px solid rgba(0, 212, 255, 0.6);
    background: linear-gradient(135deg,
        rgba(138, 43, 226, 0.3) 0%,
        rgba(0, 191, 255, 0.3) 50%,
        rgba(0, 212, 255, 0.3) 100%);
    backdrop-filter: blur(10px);
    box-shadow:
        inset 0 0 20px rgba(255, 255, 255, 0.1),
        0 0 30px rgba(0, 212, 255, 0.3);
}

.face-front {
    transform: rotateY(0deg) translateZ(60px);
}

.face-back {
    transform: rotateY(180deg) translateZ(60px);
}

.face-right {
    transform: rotateY(90deg) translateZ(60px);
}

.face-left {
    transform: rotateY(-90deg) translateZ(60px);
}

.face-top {
    transform: rotateX(90deg) translateZ(60px);
}

.face-bottom {
    transform: rotateX(-90deg) translateZ(60px);
}

/* Floating Rings */
.floating-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
}

.ring {
    position: absolute;
    border: 3px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg,
        rgba(138, 43, 226, 0.6),
        rgba(0, 191, 255, 0.6)) border-box;
    background-clip: border-box;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.ring-1 {
    top: 50%;
    left: 50%;
    width: 160px;
    height: 160px;
    transform: translate(-50%, -50%) rotateX(0deg);
    animation: ringRotate1 20s linear infinite;
}

.ring-2 {
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    transform: translate(-50%, -50%) rotateX(60deg);
    animation: ringRotate2 25s linear infinite reverse;
}

.ring-3 {
    top: 50%;
    left: 50%;
    width: 240px;
    height: 240px;
    transform: translate(-50%, -50%) rotateX(120deg);
    animation: ringRotate3 30s linear infinite;
}

/* Cleaned up old Earth styles */

/* Cleaned up old Earth animations */

/* Removed old planet styles - now using beautiful 3D Earth */

/* Cleaned up - using new 3D Earth animations above */

.spline-info-overlay {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.8));
    backdrop-filter: blur(15px);
    padding: 20px 25px;
    border-radius: 20px;
    border: 2px solid rgba(138, 43, 226, 0.4);
    z-index: 20;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(138, 43, 226, 0.3);
    animation: splineOverlayGlow 4s ease-in-out infinite alternate;
}

.spline-info-overlay h3 {
    font-family: var(--font-primary);
    background: linear-gradient(45deg, #8a2be2, #00bfff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
}

.spline-info-overlay p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    margin: 0;
}

/* Spline Container Animations */
@keyframes containerFloat {
    0% {
        transform: translate(-50%, -50%) translateY(0px) rotateZ(0deg);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.2),
            0 0 50px rgba(0, 212, 255, 0.1);
    }
    100% {
        transform: translate(-50%, -50%) translateY(-10px) rotateZ(1deg);
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.3),
            0 0 70px rgba(138, 43, 226, 0.2);
    }
}

@keyframes floatParticle {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-10px) translateX(-5px) scale(0.8);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-30px) translateX(15px) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes splineOverlayGlow {
    0% {
        border-color: rgba(138, 43, 226, 0.4);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(138, 43, 226, 0.3);
    }
    100% {
        border-color: rgba(0, 191, 255, 0.6);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(0, 191, 255, 0.4);
    }
}

/* 3D Object Animations */
@keyframes cubeRotate {
    0% {
        transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
    }
    33% {
        transform: rotateX(120deg) rotateY(120deg) rotateZ(0deg);
    }
    66% {
        transform: rotateX(240deg) rotateY(240deg) rotateZ(120deg);
    }
    100% {
        transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
    }
}

@keyframes ringRotate1 {
    0% {
        transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateX(0deg) rotateY(360deg) rotateZ(0deg);
    }
}

@keyframes ringRotate2 {
    0% {
        transform: translate(-50%, -50%) rotateX(60deg) rotateY(0deg) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateX(60deg) rotateY(0deg) rotateZ(360deg);
    }
}

@keyframes ringRotate3 {
    0% {
        transform: translate(-50%, -50%) rotateX(120deg) rotateY(0deg) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateX(120deg) rotateY(360deg) rotateZ(180deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sections */
.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-color);
}

/* Planets Section */
.planets-section {
    padding: 5rem 0;
}

.planets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.planet-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
}

.planet-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.planet-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.planet-card:hover .planet-image {
    transform: scale(1.1);
}

.planet-info {
    padding: 1.5rem;
}

.planet-info h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Missions Section */
.missions-section {
    padding: 5rem 0;
    background: rgba(0, 0, 0, 0.3);
}

.missions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.mission-video video {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.mission-text h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.mission-text ul {
    margin-top: 1rem;
    padding-left: 1.5rem;
}

.mission-text li {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Gallery Section */
.gallery-section {
    padding: 5rem 0;
}

.audio-section {
    text-align: center;
    margin-bottom: 3rem;
}

.audio-section h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.audio-section audio {
    margin: 1rem 0;
    filter: sepia(100%) saturate(200%) hue-rotate(180deg);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

/* Footer */
.footer {
    background: var(--secondary-color);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 2rem 1rem;
        gap: 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-3d {
        height: 400px;
        order: -1;
    }

    .spline-container {
        max-width: 350px;
        max-height: 350px;
    }

    .spline-info-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 15px 20px;
    }

    .planet-info-overlay {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 15px 20px;
    }

    .missions-content {
        grid-template-columns: 1fr;
    }

    .planets-grid {
        grid-template-columns: 1fr;
    }

    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .hero-3d {
        height: 350px;
    }

    .spline-container {
        max-width: 280px;
        max-height: 280px;
    }

    .spline-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 15px;
    }

    .planet-info-overlay {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 15px;
    }

    .image-gallery {
        grid-template-columns: 1fr;
    }
}
