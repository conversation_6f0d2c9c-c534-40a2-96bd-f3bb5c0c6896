<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exploración Espacial - Viaje a las Estrellas</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-brand">
                <svg class="logo-svg" width="40" height="40" viewBox="0 0 100 100">
                    <circle cx="50" cy="50" r="45" fill="none" stroke="#00d4ff" stroke-width="3"/>
                    <circle cx="50" cy="50" r="25" fill="#00d4ff" opacity="0.3"/>
                    <circle cx="50" cy="50" r="8" fill="#ffffff"/>
                    <path d="M20 50 L80 50 M50 20 L50 80" stroke="#00d4ff" stroke-width="2"/>
                </svg>
                <span>SpaceExplorer</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home">Inicio</a></li>
                <li><a href="#planets">Planetas</a></li>
                <li><a href="#missions">Misiones</a></li>
                <li><a href="#gallery">Galería</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section with 3D Element -->
    <section id="home" class="hero">
        <div class="stars-background">
            <div class="star"></div>
            <div class="star"></div>
            <div class="star"></div>
            <div class="star"></div>
            <div class="star"></div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">Explora el Universo</h1>
            <p class="hero-subtitle">Descubre los misterios del cosmos a través de la tecnología más avanzada</p>
            <button class="cta-button">Comenzar Exploración</button>
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">8</span>
                    <span class="stat-label">Planetas</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">200+</span>
                    <span class="stat-label">Lunas</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">∞</span>
                    <span class="stat-label">Estrellas</span>
                </div>
            </div>
        </div>
        <div class="hero-3d">
            <!-- Elegant Spline 3D Object -->
            <div class="spline-container">
                <!-- Real Spline 3D Model -->
                <spline-viewer url="https://prod.spline.design/6Wq1Q7YGyM-iab9i/scene.splinecode"></spline-viewer>

                <!-- Fallback 3D Object -->
                <div class="fallback-3d-object">
                    <div class="geometric-shape">
                        <div class="shape-face face-front"></div>
                        <div class="shape-face face-back"></div>
                        <div class="shape-face face-right"></div>
                        <div class="shape-face face-left"></div>
                        <div class="shape-face face-top"></div>
                        <div class="shape-face face-bottom"></div>
                    </div>
                    <div class="floating-rings">
                        <div class="ring ring-1"></div>
                        <div class="ring ring-2"></div>
                        <div class="ring ring-3"></div>
                    </div>
                </div>

                <div class="spline-background-effects">
                    <div class="floating-particle"></div>
                    <div class="floating-particle"></div>
                    <div class="floating-particle"></div>
                    <div class="floating-particle"></div>
                    <div class="floating-particle"></div>
                </div>
            </div>
            <div class="spline-info-overlay">
                <h3>✨ Elemento 3D Elegante</h3>
                <p>Objeto interactivo con efectos visuales</p>
            </div>
        </div>
    </section>

    <!-- Planets Section -->
    <section id="planets" class="planets-section">
        <div class="container">
            <h2 class="section-title">Planetas del Sistema Solar</h2>
            <div class="planets-grid">
                <div class="planet-card">
                    <img src="https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=400&h=400&fit=crop" alt="Marte" class="planet-image">
                    <div class="planet-info">
                        <h3>Marte</h3>
                        <p>El planeta rojo, objetivo de futuras misiones tripuladas.</p>
                    </div>
                </div>
                <div class="planet-card">
                    <img src="https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop" alt="Júpiter" class="planet-image">
                    <div class="planet-info">
                        <h3>Júpiter</h3>
                        <p>El gigante gaseoso con más de 80 lunas conocidas.</p>
                    </div>
                </div>
                <div class="planet-card">
                    <img src="https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400&h=400&fit=crop" alt="Tierra" class="planet-image">
                    <div class="planet-info">
                        <h3>Tierra</h3>
                        <p>Nuestro hogar, el único planeta conocido con vida.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Missions Section with Video -->
    <section id="missions" class="missions-section">
        <div class="container">
            <h2 class="section-title">Misiones Espaciales</h2>
            <div class="missions-content">
                <div class="mission-video">
                    <video controls poster="https://images.unsplash.com/photo-1517976487492-5750f3195933?w=800&h=450&fit=crop">
                        <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
                        Tu navegador no soporta el elemento video.
                    </video>
                </div>
                <div class="mission-text">
                    <h3>Artemis: Regreso a la Luna</h3>
                    <p>La misión Artemis representa el ambicioso plan de la NASA para establecer una presencia sostenible en la Luna y preparar el camino para futuras misiones a Marte.</p>
                    <ul>
                        <li>Primera mujer en la Luna</li>
                        <li>Base lunar permanente</li>
                        <li>Tecnología para Marte</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section with Audio -->
    <section id="gallery" class="gallery-section">
        <div class="container">
            <h2 class="section-title">Galería Cósmica</h2>
            <div class="audio-section">
                <h3>Sonidos del Espacio</h3>
                <audio controls>
                    <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
                    Tu navegador no soporta el elemento audio.
                </audio>
                <p>Escucha los misteriosos sonidos capturados por nuestras sondas espaciales.</p>
            </div>
            <div class="image-gallery">
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=300&h=300&fit=crop" alt="Nebulosa">
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=300&h=300&fit=crop" alt="Galaxia">
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1517976487492-5750f3195933?w=300&h=300&fit=crop" alt="Astronauta">
                </div>
                <div class="gallery-item">
                    <img src="https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=300&h=300&fit=crop" alt="Superficie marciana">
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>SpaceExplorer</h4>
                    <p>Explorando el cosmos, una estrella a la vez.</p>
                </div>
                <div class="footer-section">
                    <h4>Enlaces</h4>
                    <ul>
                        <li><a href="#home">Inicio</a></li>
                        <li><a href="#planets">Planetas</a></li>
                        <li><a href="#missions">Misiones</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contacto</h4>
                    <p><EMAIL></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SpaceExplorer. Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <!-- Spline Viewer Script -->
    <script type="module" src="https://unpkg.com/@splinetool/viewer@1.10.3/build/spline-viewer.js"></script>

    <!-- Smooth Scroll Script -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Spline 3D model loading
        document.addEventListener('DOMContentLoaded', function() {
            const splineViewer = document.querySelector('spline-viewer');
            const fallbackObject = document.querySelector('.fallback-3d-object');

            if (splineViewer && fallbackObject) {
                let splineLoaded = false;

                // Show fallback after 5 seconds if Spline hasn't loaded
                const fallbackTimer = setTimeout(() => {
                    if (!splineLoaded) {
                        console.log('Spline 3D model failed to load, showing elegant CSS fallback');
                        splineViewer.style.opacity = '0';
                        setTimeout(() => {
                            splineViewer.style.display = 'none';
                            fallbackObject.style.display = 'block';
                            fallbackObject.style.opacity = '1';
                        }, 500);
                    }
                }, 5000);

                // If Spline loads successfully
                splineViewer.addEventListener('load', function() {
                    console.log('Spline 3D model loaded successfully!');
                    splineLoaded = true;
                    clearTimeout(fallbackTimer);
                    fallbackObject.style.display = 'none';
                    splineViewer.style.opacity = '1';
                });

                // If Spline fails to load
                splineViewer.addEventListener('error', function() {
                    console.log('Spline failed to load, showing CSS fallback');
                    splineLoaded = false;
                    clearTimeout(fallbackTimer);
                    splineViewer.style.opacity = '0';
                    setTimeout(() => {
                        splineViewer.style.display = 'none';
                        fallbackObject.style.display = 'block';
                        fallbackObject.style.opacity = '1';
                    }, 500);
                });

                // Initial loading state
                splineViewer.style.opacity = '0.7';
                splineViewer.style.transition = 'opacity 0.5s ease';
            }
        });
    </script>
</body>
</html>
