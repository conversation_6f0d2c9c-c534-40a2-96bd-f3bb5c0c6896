# SpaceExplorer - Página Web Multimedia con Elementos 3D

## Descripción del Proyecto

SpaceExplorer es una página web multimedia interactiva sobre **exploración espacial** que integra elementos 3D utilizando Spline, cumpliendo con todos los requisitos del trabajo final.

## 📋 Cumplimiento de Requisitos

### ✅ Tema: Exploración Espacial
La página web aborda el tema de la exploración espacial, incluyendo:
- Misiones lunares (Artemis)
- Planetas del sistema solar
- Estación Espacial Internacional
- Vistas de la Tierra desde el espacio

### ✅ Elementos Multimedia Requeridos
- **📷 Imágenes**: Planetas, galaxias, astronautas (Unsplash)
- **🎥 Video**: Vistas de la Tierra desde la Estación Espacial
- **🔊 Audio**: Sonidos del espacio y comunicaciones NASA
- **🎨 SVG**: Logo animado y iconos vectoriales

### ✅ Elemento 3D con Spline
- Cubo 3D interactivo con fondo azul eléctrico
- Integración completa con efectos visuales
- Carga optimizada y responsive

### ✅ Animaciones CSS
- **Keyframes**: Rotación del logo, efectos de partículas
- **Transitions**: Hover effects, transformaciones suaves

### ✅ Diseño Responsive
- **CSS Grid**: Layout principal y galerías
- **Flexbox**: Navegación y componentes
- **Media Queries**: Adaptación móvil/tablet/desktop

## Características Implementadas

### ✅ Estructura HTML
- Estructura semántica con etiquetas apropiadas
- Navegación responsive
- Secciones organizadas (Hero, Planetas, Misiones, Galería)

### ✅ Estilos CSS
- Variables CSS para consistencia
- Diseño moderno con gradientes y efectos
- Tipografías personalizadas (Orbitron y Roboto)

### ✅ Elementos Multimedia

#### Imágenes
- Imágenes de planetas del sistema solar
- Galería de imágenes espaciales
- Todas las imágenes son responsive

#### Video
- Video de misiones espaciales con controles
- Poster personalizado
- Responsive design

#### Audio
- Elemento de audio con sonidos del espacio
- Controles personalizados
- Descripción contextual

#### SVG
- Logo animado en el header
- Iconos vectoriales escalables
- Animación de rotación continua

### ✅ Elemento 3D con Spline
- Integración de Spline Viewer con **planeta interactivo**
- Modelo 3D de planeta con rotación y zoom
- Efectos visuales envolventes (gradientes radiales, sombras cónicas)
- Overlay informativo con animaciones pulse y bounce
- Fondo de estrellas parpadeantes
- Carga asíncrona del contenido 3D

### ✅ Animaciones CSS
- **Keyframes**: Rotación del logo SVG
- **Transitions**: Efectos hover en tarjetas y botones
- **Animaciones de entrada**: slideInLeft y slideInRight
- **Efectos de transformación**: scale, translateY

### ✅ Diseño Responsive
- **CSS Grid**: Layout principal y galerías
- **Flexbox**: Navegación y elementos internos
- **Media queries**: Adaptación para móviles y tablets
- **Breakpoints**: 768px y 480px

## Estructura de Archivos

```
tpfinal/
├── index.html          # Página principal
├── styles.css          # Estilos CSS
└── README.md          # Documentación
```

## Tecnologías Utilizadas

- **HTML5**: Estructura semántica
- **CSS3**: Estilos, animaciones y responsive design
- **Spline**: Elementos 3D interactivos
- **Google Fonts**: Tipografías Orbitron y Roboto
- **Unsplash**: Imágenes de alta calidad

## Características Responsive

### Desktop (>768px)
- Layout de dos columnas en hero
- Grid de 3 columnas para planetas
- Navegación completa visible

### Tablet (768px)
- Layout de una columna
- Grid adaptativo
- Navegación simplificada

### Mobile (480px)
- Diseño optimizado para móvil
- Imágenes apiladas
- Tipografías reducidas

## Elementos 3D

El proyecto integra un modelo 3D de Spline que muestra:
- Escena espacial interactiva
- Controles de cámara
- Carga optimizada
- Integración seamless con el diseño

## Animaciones Implementadas

1. **Logo rotativo**: Animación continua del SVG (rotate)
2. **Estrellas parpadeantes**: Efecto twinkle en el fondo
3. **Planeta envolvente**: Gradiente cónico rotativo
4. **Overlay pulsante**: Animación pulse en información del planeta
5. **Icono interactivo**: Animación bounce en hint
6. **Hover effects**: Transformaciones en tarjetas y botones
7. **Slide animations**: Entrada de elementos (slideInLeft/Right)
8. **Button effects**: Elevación y sombras
9. **Image scaling**: Zoom en hover

## Cómo Ejecutar

1. Abrir `index.html` en un navegador web moderno
2. Asegurar conexión a internet para:
   - Fuentes de Google
   - Imágenes de Unsplash
   - Spline Viewer
   - Contenido multimedia

## Navegadores Compatibles

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Créditos

- **Imágenes**: Unsplash
- **Fuentes**: Google Fonts
- **3D**: Spline
- **Desarrollo**: Proyecto académico

---

*Proyecto desarrollado como trabajo final para la materia de Desarrollo Web*
